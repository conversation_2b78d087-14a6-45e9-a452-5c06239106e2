'use client';

import { useState } from 'react';

interface FormData {
  name: string;
  email: string;
  phone: string;
  position: string;
  experience: string;
  availability: string;
  message: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function JobApplicationForm() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    position: '',
    experience: '',
    availability: '',
    message: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Email configurabile per le candidature
  const JOB_EMAIL = '<EMAIL>'; // Sostituisci con la tua email

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Il nome è obbligatorio';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email è obbligatoria';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Inserisci un\'email valida';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Il telefono è obbligatorio';
    }

    if (!formData.position.trim()) {
      newErrors.position = 'Seleziona una posizione';
    }

    if (!formData.experience.trim()) {
      newErrors.experience = 'Indica la tua esperienza';
    }

    if (!formData.availability.trim()) {
      newErrors.availability = 'Indica la tua disponibilità';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Qui dovresti implementare l'invio email reale
      // Per ora simuliamo l'invio
      const emailBody = `
        Nuova candidatura da ${formData.name}
        
        Dettagli candidatura:
        - Nome: ${formData.name}
        - Email: ${formData.email}
        - Telefono: ${formData.phone}
        - Posizione: ${formData.position}
        - Esperienza: ${formData.experience}
        - Disponibilità: ${formData.availability}
        - Messaggio: ${formData.message || 'Nessun messaggio aggiuntivo'}
      `;

      // Simula chiamata API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Job application data:', formData);
      console.log('Email body:', emailBody);
      console.log('Send to:', JOB_EMAIL);
      
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error submitting job application:', error);
      alert('Si è verificato un errore. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <div className="bg-green-100 border border-green-400 text-green-700 px-6 py-8 rounded-lg mb-8">
          <h3 className="font-heading text-2xl font-semibold mb-4">
            Candidatura Inviata!
          </h3>
          <p className="mb-4">
            Grazie {formData.name}! La tua candidatura è stata inviata con successo.
          </p>
          <p className="text-sm">
            Ti contatteremo presto per valutare la tua candidatura.
          </p>
        </div>
        <button
          onClick={() => {
            setIsSubmitted(false);
            setFormData({
              name: '',
              email: '',
              phone: '',
              position: '',
              experience: '',
              availability: '',
              message: ''
            });
          }}
          className="btn-romoletto"
        >
          Nuova Candidatura
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        
        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-dark-gray mb-2">
            Nome Completo *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Il tuo nome completo"
          />
          {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
        </div>

        {/* Email */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-dark-gray mb-2">
            Email *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
              errors.email ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="<EMAIL>"
          />
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
        </div>

        {/* Phone */}
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-dark-gray mb-2">
            Telefono *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
              errors.phone ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="+39 ************"
          />
          {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
        </div>

        {/* Position */}
        <div>
          <label htmlFor="position" className="block text-sm font-medium text-dark-gray mb-2">
            Posizione di Interesse *
          </label>
          <select
            id="position"
            name="position"
            value={formData.position}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
              errors.position ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Seleziona una posizione</option>
            <option value="cameriere">Cameriere/a</option>
            <option value="cuoco">Cuoco/a</option>
            <option value="aiuto-cuoco">Aiuto Cuoco/a</option>
            <option value="barista">Barista</option>
            <option value="lavapiatti">Lavapiatti</option>
            <option value="altro">Altro</option>
          </select>
          {errors.position && <p className="text-red-500 text-sm mt-1">{errors.position}</p>}
        </div>

        {/* Experience */}
        <div>
          <label htmlFor="experience" className="block text-sm font-medium text-dark-gray mb-2">
            Esperienza *
          </label>
          <select
            id="experience"
            name="experience"
            value={formData.experience}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
              errors.experience ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Seleziona la tua esperienza</option>
            <option value="nessuna">Nessuna esperienza</option>
            <option value="1-2-anni">1-2 anni</option>
            <option value="3-5-anni">3-5 anni</option>
            <option value="oltre-5-anni">Oltre 5 anni</option>
          </select>
          {errors.experience && <p className="text-red-500 text-sm mt-1">{errors.experience}</p>}
        </div>

        {/* Availability */}
        <div>
          <label htmlFor="availability" className="block text-sm font-medium text-dark-gray mb-2">
            Disponibilità *
          </label>
          <select
            id="availability"
            name="availability"
            value={formData.availability}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
              errors.availability ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Seleziona la tua disponibilità</option>
            <option value="full-time">Full-time</option>
            <option value="part-time">Part-time</option>
            <option value="weekend">Solo weekend</option>
            <option value="sera">Solo sera</option>
            <option value="flessibile">Flessibile</option>
          </select>
          {errors.availability && <p className="text-red-500 text-sm mt-1">{errors.availability}</p>}
        </div>

        {/* Message */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-dark-gray mb-2">
            Messaggio Aggiuntivo
          </label>
          <textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent"
            placeholder="Raccontaci qualcosa di te, le tue motivazioni, esperienze particolari..."
          />
        </div>

        {/* Submit Button */}
        <div className="text-center">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`btn-romoletto text-lg px-8 py-4 ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105 transform transition-all duration-300'
            }`}
          >
            {isSubmitting ? 'Invio in corso...' : 'Invia Candidatura'}
          </button>
        </div>

        <p className="text-sm text-gray-600 text-center">
          * Campi obbligatori. Ti contatteremo per valutare la tua candidatura.
        </p>
      </form>
    </div>
  );
}
