'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { MdRestaurant } from 'react-icons/md';
import { FaFacebook, FaInstagram, FaTripadvisor } from 'react-icons/fa';

export default function TopBar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    const newState = !isMobileMenuOpen;
    setIsMobileMenuOpen(newState);

    // Google Analytics event tracking
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', newState ? 'menu_open' : 'menu_close', {
        event_category: 'navigation',
        event_label: 'sidebar_menu'
      });
    }
  };

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? 'bg-primary-red shadow-lg'
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Left side - Prenota Tavolo Button */}
            <div className="flex items-center">
              <Link
                href="https://romoletto.plateform.app/reserve"
                className={`inline-flex items-center justify-center gap-2 px-6 py-3 rounded-md font-semibold transition-all duration-300 hover:scale-105 ${
                  isScrolled
                    ? 'bg-primary-yellow text-primary-red hover:bg-opacity-90'
                    : 'bg-primary-red text-primary-yellow hover:bg-opacity-90'
                }`}
              >
                <MdRestaurant className="text-xl" />
                Prenota Tavolo
              </Link>
            </div>

            {/* Center - Logo */}
            <Link href="/" className="flex-shrink-0 flex items-center gap-3">
              <Image
                src="/logo/logo_piccolo.png"
                alt="Romoletto Logo"
                width={120}
                height={40}
                className="h-14 w-auto"
                priority
              />

              <Image
                src="/logo/romo_scritta.png"
                alt="Romoletto Logo"
                width={100}
                height={25}
                className="w-auto hidden md:block"
                priority
              />

            </Link>

            {/* Right side - Menu button */}
            <div className="flex items-center">
              <button
                onClick={toggleMobileMenu}
                className={`inline-flex items-center justify-center p-2 rounded-md transition-all duration-300 hover:scale-110 ${
                  isScrolled
                    ? 'text-primary-yellow hover:text-primary-yellow hover:bg-primary-yellow hover:bg-opacity-20'
                    : 'text-primary-yellow hover:text-off-white hover:bg-white hover:bg-opacity-20'
                }`}
                aria-expanded="false"
              >
                <span className="sr-only">Apri menu principale</span>
                {/* Hamburger icon */}
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Sidebar for both Desktop and Mobile */}
      <div
        className={`fixed inset-0 z-50 transform transition-transform duration-300 ease-in-out ${
          isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={toggleMobileMenu}
        ></div>

        {/* Sidebar */}
        <div className="absolute right-0 top-0 h-full w-80 bg-primary-yellow shadow-xl">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-primary-red">
              <Image
                src="/logo/logo_giallo_topbar.png"
                alt="Romoletto Logo"
                width={100}
                height={33}
                className="h-8 w-auto"
              />
              <button
                onClick={toggleMobileMenu}
                className="text-primary-red hover:text-supporting-dark-red p-2"
              >
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Navigation Links */}
            <nav className="flex-1 px-4 py-6 space-y-2">
              <Link
                href="/nostra-storia"
                className="group block text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
                onClick={toggleMobileMenu}
              >
                <span className="flex items-center">
                  La Nostra Storia
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">→</span>
                </span>
              </Link>
              <button
                onClick={() => {
                  window.open('/pdf/menu_romoletto.pdf', '_blank');
                  toggleMobileMenu();
                }}
                className="group block w-full text-left text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
              >
                <span className="flex items-center">
                  Menu
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">↗</span>
                </span>
              </button>
              <Link
                href="/lavora-con-noi"
                className="group block text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
                onClick={toggleMobileMenu}
              >
                <span className="flex items-center">
                  Lavora con Noi
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">→</span>
                </span>
              </Link>
              <Link
                href="/prenotazione"
                className="group block text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
                onClick={toggleMobileMenu}
              >
                <span className="flex items-center">
                  Prenotazione
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">→</span>
                </span>
              </Link>
            </nav>

            {/* Social Buttons */}
            <div className="px-4 py-4 border-t border-primary-red border-opacity-20">
              <p className="text-primary-red font-semibold text-sm mb-3 text-center">Seguici sui social</p>
              <div className="flex justify-center gap-3">
                {/* Facebook */}
                <a 
                  href="https://www.facebook.com/p/Romoletto-Roma-61553540753475/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#1877F2] text-white rounded-full p-3 hover:scale-110 transition-transform duration-300"
                  onClick={toggleMobileMenu}
                >
                  <FaFacebook className="text-lg" />
                </a>

                {/* Instagram */}
                <a 
                  href="https://www.instagram.com/romolettoroma/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gradient-to-r from-[#E4405F] via-[#F56040] to-[#FFDC80] text-white rounded-full p-3 hover:scale-110 transition-transform duration-300"
                  onClick={toggleMobileMenu}
                >
                  <FaInstagram className="text-lg" />
                </a>

                {/* TripAdvisor */}
                <a 
                  href="https://www.tripadvisor.it/Restaurant_Review-g187791-d27496059-Reviews-Romoletto-Rome_Lazio.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#00AA6C] text-white rounded-full p-3 hover:scale-110 transition-transform duration-300"
                  onClick={toggleMobileMenu}
                >
                  <FaTripadvisor className="text-lg" />
                </a>
              </div>
            </div>

            {/* CTA Button */}
            <div className="p-4">
              <Link
                href="https://romoletto.plateform.app/reserve"
                className="btn-romoletto w-full justify-center"
                onClick={toggleMobileMenu}
              >
                <MdRestaurant className="text-xl" />
                Prenota Ora
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
