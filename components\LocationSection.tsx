import { FaSubway, FaBus, FaCar, FaWalking } from 'react-icons/fa';

export default function LocationSection() {
  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="font-heading text-4xl font-bold text-primary-red mb-6">
            <PERSON>
          </h2>
          <div className="w-24 h-1 bg-primary-yellow mx-auto mb-8"></div>
          <p className="text-lg text-dark-gray max-w-3xl mx-auto">
            Vieni a trovarci nel cuore di Roma, a pochi passi da Campo de&apos; Fiori.
            Ti aspettiamo per farti assaporare l&apos;autentica cucina romana!
          </p>
        </div>

        <div className="space-y-8">
          {/* Google Maps - Full Width */}
          <div>
            <div className="relative h-96 rounded-lg overflow-hidden shadow-xl">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2969.8234567890123!2d12.4726!3d41.8955!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x132f604f678a8b29%3A0x1234567890abcdef!2sPiazza%20Campo%20de'%20Fiori%2C%2047%2C%2000186%20Roma%20RM%2C%20Italy!5e0!3m2!1sen!2sit!4v1234567890123!5m2!1sen!2sit"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Mappa Romoletto - Piazza Campo de' Fiori 47, Roma"
              ></iframe>
            </div>
          </div>

          {/* Contact Information - Full Width */}
          <div>
            <div className="bg-primary-yellow p-8 rounded-lg shadow-xl">
              <h3 className="font-heading text-3xl font-semibold text-primary-red mb-8 text-center">
                Informazioni di Contatto
              </h3>

              {/* Contact Info Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-6 mb-8">
                {/* Address */}
                <div className="text-center">
                  <div className="bg-primary-red text-primary-yellow rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h4 className="font-semibold text-primary-red mb-2">Indirizzo</h4>
                  <p className="text-dark-gray text-sm">
                    Piazza Campo de&apos; Fiori 47<br />
                    00186 Roma, Italia
                  </p>
                </div>

                {/* Phone */}


                {/* Email */}
                <div className="text-center">
                  <div className="bg-primary-red text-primary-yellow rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                  <h4 className="font-semibold text-primary-red mb-2">Email</h4>
                  <p className="text-dark-gray text-sm"><EMAIL></p>
                </div>

                {/* Opening Hours */}
                <div className="text-center">
                  <div className="bg-primary-red text-primary-yellow rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h4 className="font-semibold text-primary-red mb-2">Orari</h4>
                  <div className="text-dark-gray text-sm space-y-1">
                    <p><strong>Pranzo:</strong> 12:00-15:00</p>
                    <p><strong>Cena:</strong> 19:00-24:00</p>
                    <p className="text-xs text-primary-red font-semibold">Aperto tutti i giorni</p>
                  </div>
                </div>
              </div>

              {/* Directions */}
              <div className="bg-primary-red text-primary-yellow p-6 rounded-lg">
                <h4 className="font-heading text-xl font-semibold mb-4 text-center">Come Raggiungerci</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                      <FaSubway className="w-6 h-6" />
                    </div>
                    <p className="text-sm"><strong>Metro</strong><br />Linea B - Colosseo<br />(15 min a piedi)</p>
                  </div>
                  <div>
                    <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                      <FaBus className="w-6 h-6" />
                    </div>
                    <p className="text-sm"><strong>Bus</strong><br />Linee 40, 46, 62<br />64, 916</p>
                  </div>
                  <div>
                    <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                      <FaCar className="w-6 h-6" />
                    </div>
                    <p className="text-sm"><strong>Auto</strong><br />Parcheggi nelle<br />vicinanze</p>
                  </div>
                  <div>
                    <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                      <FaWalking className="w-6 h-6" />
                    </div>
                    <p className="text-sm"><strong>A piedi</strong><br />Centro storico<br />raggiungibile</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
