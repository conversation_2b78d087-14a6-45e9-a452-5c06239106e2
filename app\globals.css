@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Romoletto Brand Colors */
  --primary-red: #b22013;
  --primary-yellow: #F7C629;
  --supporting-dark-red: #B1220E;
  --off-white: #F5F5F5;
  --dark-gray: #222222;

  --background: #ffffff;
  --foreground: #222222;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-source-sans), 'Source Sans Pro', sans-serif;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-oswald), 'Oswald', sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

/* Romo<PERSON>o Button Styles */
.btn-romoletto {
  background-color: var(--primary-red);
  color: var(--primary-yellow);
  border: none;
  padding: 12px 24px;
  font-family: var(--font-source-sans), 'Source Sans Pro', sans-serif;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-align: center;
}

.btn-romoletto:hover {
  background-color: var(--primary-yellow);
  color: var(--primary-red);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(178, 32, 19, 0.3);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Pattern background utility - REMOVED */
