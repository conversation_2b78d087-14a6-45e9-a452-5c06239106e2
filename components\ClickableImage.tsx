'use client';

import { useState } from 'react';
import Image from 'next/image';
import ImageModal from './ImageModal';

interface ClickableImageProps {
  src: string;
  alt: string;
  className?: string;
  fill?: boolean;
  width?: number;
  height?: number;
  sizes?: string;
  priority?: boolean;
}

export default function ClickableImage({ 
  src, 
  alt, 
  className = '', 
  fill = false,
  width,
  height,
  sizes,
  priority = false
}: ClickableImageProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleImageClick = () => {
    setIsModalOpen(true);
    
    // Google Analytics event tracking
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'image_click', {
        event_category: 'engagement',
        event_label: alt,
        image_src: src
      });
    }
  };

  return (
    <>
      <div
        className={`relative cursor-pointer hover:opacity-90 transition-opacity duration-300 ${className}`}
        onClick={handleImageClick}
      >
        {fill ? (
          <Image
            src={src}
            alt={alt}
            fill
            className="object-cover hover:scale-105 transition-transform duration-300"
            sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
            priority={priority}
          />
        ) : (
          <Image
            src={src}
            alt={alt}
            width={width || 400}
            height={height || 300}
            className="object-cover hover:scale-105 transition-transform duration-300"
            sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
            priority={priority}
          />
        )}

        {/* Overlay icon */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 bg-black bg-opacity-30 rounded-lg">
          <div className="bg-primary-red text-primary-yellow rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </div>
        </div>
      </div>

      <ImageModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        imageSrc={src}
        imageAlt={alt}
      />
    </>
  );
}
