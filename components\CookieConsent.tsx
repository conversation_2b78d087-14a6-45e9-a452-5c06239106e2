'use client';

import { useState, useEffect } from 'react';

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
}

declare global {
  interface Window {
    gtag: (...args: unknown[]) => void;
    dataLayer: Record<string, unknown>[];
  }
}

export default function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('cookie-consent');
    if (!consent) {
      setShowBanner(true);
    } else {
      // Load Google Analytics if consent was given
      const consentData = JSON.parse(consent);
      if (consentData.analytics) {
        loadGoogleAnalytics();
      }
    }
  }, []);

  const loadGoogleAnalytics = () => {
    // Google Analytics 4 implementation
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID';
    document.head.appendChild(script1);

    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'GA_MEASUREMENT_ID', {
        cookie_flags: 'SameSite=None;Secure'
      });
    `;
    document.head.appendChild(script2);
  };

  const acceptAll = () => {
    const consent = {
      necessary: true,
      analytics: true,
      marketing: true,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookie-consent', JSON.stringify(consent));
    
    // Update Google Consent Mode
    if (typeof window.gtag !== 'undefined') {
      window.gtag('consent', 'update', {
        analytics_storage: 'granted',
        ad_storage: 'granted',
        ad_user_data: 'granted',
        ad_personalization: 'granted'
      });
    }
    
    loadGoogleAnalytics();
    setShowBanner(false);
  };

  const acceptNecessary = () => {
    const consent = {
      necessary: true,
      analytics: false,
      marketing: false,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookie-consent', JSON.stringify(consent));
    
    // Update Google Consent Mode
    if (typeof window.gtag !== 'undefined') {
      window.gtag('consent', 'update', {
        analytics_storage: 'denied',
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied'
      });
    }
    
    setShowBanner(false);
  };

  const savePreferences = (preferences: CookiePreferences) => {
    const consent = {
      necessary: true,
      analytics: preferences.analytics,
      marketing: preferences.marketing,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookie-consent', JSON.stringify(consent));
    
    // Update Google Consent Mode
    if (typeof window.gtag !== 'undefined') {
      window.gtag('consent', 'update', {
        analytics_storage: preferences.analytics ? 'granted' : 'denied',
        ad_storage: preferences.marketing ? 'granted' : 'denied',
        ad_user_data: preferences.marketing ? 'granted' : 'denied',
        ad_personalization: preferences.marketing ? 'granted' : 'denied'
      });
    }
    
    if (preferences.analytics) {
      loadGoogleAnalytics();
    }
    
    setShowBanner(false);
    setShowPreferences(false);
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Cookie Banner */}
      <div className="fixed bottom-0 left-0 right-0 bg-primary-red text-primary-yellow p-4 shadow-2xl z-50 border-t-4 border-primary-yellow">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-2">🍪</span>
                <h3 className="font-heading text-lg font-semibold">Cookie Policy</h3>
              </div>
              <p className="text-sm leading-relaxed">
                Utilizziamo cookie per migliorare la tua esperienza sul nostro sito.
                Alcuni cookie sono necessari per il funzionamento del sito, altri ci aiutano
                a capire come interagisci con i nostri contenuti.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 min-w-fit">
              <button
                onClick={() => setShowPreferences(true)}
                className="px-4 py-2 text-sm border-2 border-primary-yellow text-primary-yellow hover:bg-primary-yellow hover:text-primary-red transition-all duration-300 rounded-lg font-semibold"
              >
                Personalizza
              </button>
              <button
                onClick={acceptNecessary}
                className="px-4 py-2 text-sm border-2 border-primary-yellow text-primary-yellow hover:bg-primary-yellow hover:text-primary-red transition-all duration-300 rounded-lg font-semibold"
              >
                Solo Necessari
              </button>
              <button
                onClick={acceptAll}
                className="bg-primary-yellow text-primary-red px-4 py-2 text-sm rounded-lg font-semibold hover:bg-off-white hover:text-supporting-dark-red transition-all duration-300 shadow-lg"
              >
                Accetta Tutti
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Preferences Modal */}
      {showPreferences && (
        <CookiePreferencesModal
          onSave={savePreferences}
          onClose={() => setShowPreferences(false)}
        />
      )}
    </>
  );
}

function CookiePreferencesModal({ onSave, onClose }: { onSave: (prefs: CookiePreferences) => void; onClose: () => void }) {
  const [analytics, setAnalytics] = useState(false);
  const [marketing, setMarketing] = useState(false);

  const handleSave = () => {
    onSave({ necessary: true, analytics, marketing });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6 border-4 border-primary-yellow shadow-2xl">
        <div className="flex items-center mb-4">
          <span className="text-2xl mr-2">⚙️</span>
          <h3 className="font-heading text-xl font-semibold text-primary-red">Preferenze Cookie</h3>
        </div>
        
        <div className="space-y-4">
          <div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Cookie Necessari</span>
              <span className="text-sm text-gray-500">Sempre attivi</span>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Necessari per il funzionamento base del sito web.
            </p>
          </div>

          <div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Cookie Analitici</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={analytics}
                  onChange={(e) => setAnalytics(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-red"></div>
              </label>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Ci aiutano a capire come i visitatori interagiscono con il sito.
            </p>
          </div>

          <div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Cookie Marketing</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={marketing}
                  onChange={(e) => setMarketing(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-red"></div>
              </label>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Utilizzati per mostrare annunci personalizzati.
            </p>
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border-2 border-primary-red text-primary-red rounded-lg hover:bg-primary-red hover:text-white transition-all duration-300 font-semibold"
          >
            Annulla
          </button>
          <button
            onClick={handleSave}
            className="flex-1 bg-primary-red text-primary-yellow px-4 py-2 rounded-lg font-semibold hover:bg-primary-yellow hover:text-primary-red transition-all duration-300"
          >
            Salva Preferenze
          </button>
        </div>
      </div>
    </div>
  );
}
