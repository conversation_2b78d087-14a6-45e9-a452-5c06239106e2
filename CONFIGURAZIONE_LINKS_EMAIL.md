# Configurazione Links e Email - Romoletto

Questo documento spiega come configurare i link e gli indirizzi email personalizzati nel sito Romoletto.

## 📋 Indice

1. [Pagina Prenotazione](#pagina-prenotazione)
2. [Sezione Altre Sedi](#sezione-altre-sedi)
3. [Form Lavora con Noi](#form-lavora-con-noi)
4. [Implementazione Email](#implementazione-email)

---

## 🍽️ Pagina Prenotazione

### File da modificare: `app/prenotazione/page.tsx`

**Linea 10:** Sostituisci il link di prenotazione
```typescript
const RESERVATION_LINK = 'https://example.com/prenota'; // Sostituisci con il tuo link di prenotazione
```

**Esempio:**
```typescript
const RESERVATION_LINK = 'https://opentable.com/romoletto-roma';
// oppure
const RESERVATION_LINK = 'https://thefork.it/ristorante/romoletto';
// oppure
const RESERVATION_LINK = 'https://resy.com/cities/rome/romoletto';
```

### Cosa fa:
- Il pulsante "Prenota il Tavolo" reindirizzerà gli utenti al link specificato
- Si aprirà in una nuova scheda del browser
- Mantiene il design coerente con il resto del sito

---

## 🏢 Sezione Altre Sedi

### File da modificare: `components/OtherLocationsSection.tsx`

**Linee 8-9 e 15-16:** Sostituisci i link per ogni ristorante

```typescript
// Per Mariuccia
menuLink: '/menu-mariuccia', // Sostituisci con il link effettivo
reservationLink: '/prenotazione-mariuccia' // Sostituisci con il link effettivo

// Per Romoletto
menuLink: '/menu-romoletto', // Sostituisci con il link effettivo
reservationLink: '/prenotazione-romoletto' // Sostituisci con il link effettivo
```

**Esempi:**
```typescript
// Mariuccia
menuLink: 'https://mariuccia.it/menu',
reservationLink: 'https://opentable.com/mariuccia-roma'

// Romoletto
menuLink: '/pdf/menu_romoletto.pdf',
reservationLink: 'https://thefork.it/ristorante/romoletto'
```

### Cosa fanno:
- **Pulsante MENU**: Porta al menu del ristorante (PDF, pagina web, ecc.)
- **Pulsante Prenotazioni**: Porta al sistema di prenotazione del ristorante
- **Click sulla card**: Porta al link di prenotazione

---

## 💼 Form Lavora con Noi

### File da modificare: `components/JobApplicationForm.tsx`

**Linea 30:** Sostituisci l'indirizzo email per le candidature
```typescript
const JOB_EMAIL = '<EMAIL>'; // Sostituisci con la tua email
```

**Esempio:**
```typescript
const JOB_EMAIL = '<EMAIL>';
// oppure
const JOB_EMAIL = '<EMAIL>';
// oppure
const JOB_EMAIL = '<EMAIL>';
```

### Cosa fa:
- Raccoglie le candidature attraverso un form strutturato
- Include campi per: nome, email, telefono, posizione, esperienza, disponibilità, messaggio
- Attualmente simula l'invio (vedi sezione implementazione email)

---

## 📧 Implementazione Email

### Stato Attuale
Attualmente i form **simulano** l'invio delle email. I dati vengono:
- Loggati nella console del browser (F12 > Console)
- Mostrati in un messaggio di conferma
- **NON vengono effettivamente inviati via email**

### Per Implementare l'Invio Email Reale

Hai diverse opzioni:

#### Opzione 1: Servizio Email (Consigliato)
Usa servizi come **EmailJS**, **Formspree**, o **Netlify Forms**:

```typescript
// Esempio con EmailJS
import emailjs from '@emailjs/browser';

const sendEmail = async (formData) => {
  try {
    await emailjs.send(
      'YOUR_SERVICE_ID',
      'YOUR_TEMPLATE_ID',
      formData,
      'YOUR_PUBLIC_KEY'
    );
  } catch (error) {
    console.error('Error sending email:', error);
  }
};
```

#### Opzione 2: API Route Next.js
Crea un endpoint API in `app/api/send-email/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  const data = await request.json();
  
  // Configura nodemailer con il tuo provider email
  const transporter = nodemailer.createTransporter({
    // configurazione SMTP
  });
  
  // Invia email
  await transporter.sendMail({
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Nuova candidatura',
    html: `<p>Candidatura da: ${data.name}</p>`
  });
  
  return NextResponse.json({ success: true });
}
```

#### Opzione 3: Servizi Backend
Integra con servizi come **Supabase**, **Firebase**, o **AWS SES**.

---

## 🔧 Modifiche Rapide

### Per cambiare solo i link:
1. Apri i file indicati sopra
2. Cerca le righe con i commenti "Sostituisci con..."
3. Sostituisci i link placeholder con i tuoi link reali
4. Salva i file

### Per testare le modifiche:
1. Avvia il server di sviluppo: `npm run dev`
2. Naviga alle pagine modificate
3. Testa i pulsanti e i link
4. Controlla la console del browser per i log dei form

---

## 📞 Supporto

Se hai bisogno di aiuto per:
- Configurare l'invio email reale
- Modificare il design dei form
- Aggiungere nuovi campi
- Integrare con servizi esterni

Contatta il team di sviluppo con i dettagli specifici delle tue esigenze.

---

**Ultimo aggiornamento:** Gennaio 2025  
**Versione:** 1.0
