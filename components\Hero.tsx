'use client';

import { useEffect, useRef, useState } from 'react';

export default function Hero() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch((error) => {
        console.log('Video autoplay failed:', error);
      });
    }
  }, [isMobile]);

  return (
    <section className="relative h-screen w-full overflow-hidden">
      {/* Video Background */}
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover"
        autoPlay
        muted
        loop
        playsInline
        preload="metadata"
      >
        <source
          src={isMobile ? '/video/verticale.mp4' : '/video/orizzontale.mp4'}
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video>

      {/* Optional overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-30"></div>

      {/* Main Content overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center text-white px-4 max-w-4xl mx-auto">
          <h1 className="font-heading text-4xl md:text-6xl lg:text-7xl font-bold mb-4 drop-shadow-2xl">
            Romoletto
          </h1>
          <div className="w-24 md:w-48 lg:w-48 h-1 bg-primary-yellow mx-auto mb-6"></div>
          <p className="text-lg md:text-2xl lg:text-3xl font-light leading-relaxed drop-shadow-lg">
            La vera cucina romana, nel cuore di Campo de&apos; Fiori.
          </p>
        </div>
      </div>

      {/* Scroll indicator at bottom */}
      <div className="absolute inset-0 flex items-end justify-center pb-16">
        <div className="text-center">
          {/* Scroll indicator */}
          <div className="animate-bounce">
            <svg
              className="w-6 h-6 text-white mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
}
