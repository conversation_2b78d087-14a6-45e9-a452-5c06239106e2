'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

const carouselImages = [
  {
    src: '/carousel/DSC02787.webp',
    alt: 'Piatti tradizionali romani',
  },
  {
    src: '/carousel/DSC02814.webp',
    alt: 'Cucina autentica',
  },
  {
    src: '/carousel/DSC02842.webp',
    alt: 'Ingredienti freschi',
  },
  {
    src: '/carousel/DSC02861.webp',
    alt: 'Atmosfera familiare',
  },
  {
    src: '/carousel/DSC02891.webp',
    alt: 'Pizza romana croccante',
  },
  {
    src: '/carousel/DSC02915.webp',
    alt: 'Pasta fatta in casa',
  },
  {
    src: '/carousel/DSC02955.webp',
    alt: 'Tradizione culinaria',
  },
];

export default function ImageCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === carouselImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 4000); // Change image every 4 seconds

    return () => clearInterval(interval);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? carouselImages.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === carouselImages.length - 1 ? 0 : currentIndex + 1);
  };

  return (
    <section className="py-16 bg-off-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="font-heading text-4xl font-bold text-dark-gray mb-4">
            La Qualità dei Nostri Piatti
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Ogni piatto racconta una storia di passione, tradizione e ingredienti selezionati. 
            Dalla pasta fatta in casa alle pizze romane croccanti, ogni boccone è un viaggio 
            nel cuore della cucina autentica.
          </p>
        </div>

        <div className="relative">
          {/* Main carousel container */}
          <div className="relative h-96 md:h-[500px] rounded-lg overflow-hidden shadow-2xl">
            {carouselImages.map((image, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-opacity duration-1000 ${
                  index === currentIndex ? 'opacity-100' : 'opacity-0'
                }`}
              >
                <Image
                  src={image.src}
                  alt={image.alt}
                  fill
                  className="object-cover"
                  priority={index === 0}
                />
              </div>
            ))}

            {/* Navigation arrows */}
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
              aria-label="Immagine precedente"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
              aria-label="Immagine successiva"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Dots indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {carouselImages.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all ${
                  index === currentIndex
                    ? 'bg-primary-red scale-125'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Vai all'immagine ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
