import Image from 'next/image';
import Link from 'next/link';

interface RestaurantCardProps {
  name: string;
  address: string;
  location: string;
  imageSrc: string;
  menuLink: string;
  reservationLink: string;
}

export default function RestaurantCard({
  name,
  address,
  location,
  imageSrc,
  menuLink,
  reservationLink
}: RestaurantCardProps) {
  return (
    <div className="relative group cursor-pointer overflow-hidden rounded-lg shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
      {/* Background Image */}
      <div className="relative h-[500px] w-full">
        <Image
          src={imageSrc}
          alt={`${name} - Ristorante`}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-110"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-50 transition-all duration-300"></div>
        
        {/* Content Overlay */}
        <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
          {/* Restaurant Name and Address */}
          <div className="mb-20">
            <h3 className="font-heading text-3xl font-bold mb-2 text-primary-yellow drop-shadow-lg">
              {name.toUpperCase()}
            </h3>
            <p className="text-lg font-medium mb-1 drop-shadow-md">
              {address}
            </p>
            <p className="text-base opacity-90 drop-shadow-md">
              {location}
            </p>
          </div>
        </div>
      </div>
      
      {/* Click overlay for entire card */}
      <Link href={reservationLink} className="absolute inset-0 z-10">
        <span className="sr-only">Vai a {name}</span>
      </Link>
      
      {/* Buttons need higher z-index to be clickable */}
      <div className="absolute bottom-6 left-6 right-6 flex gap-4 z-20">
        <Link
          href={menuLink}
          className="flex-1 bg-primary-yellow text-primary-red px-4 py-3 rounded-lg font-semibold text-center hover:bg-white hover:text-primary-red transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          MENU
        </Link>
        <Link
          href={reservationLink}
          className="flex-1 bg-primary-red text-primary-yellow px-4 py-3 rounded-lg font-semibold text-center hover:bg-supporting-dark-red transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          Prenotazioni
        </Link>
      </div>
    </div>
  );
}
