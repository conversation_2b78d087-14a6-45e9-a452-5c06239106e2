'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface SpecialDishesCarouselProps {
  images: string[];
  title?: string;
  autoPlay?: boolean;
  interval?: number;
}

export default function SpecialDishesCarousel({ 
  images, 
  title = "I Nostri Piatti Speciali",
  autoPlay = true, 
  interval = 3000 
}: SpecialDishesCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Autoplay effect
  useEffect(() => {
    if (!autoPlay || images.length === 0) return;

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, interval);

    return () => clearInterval(timer);
  }, [autoPlay, interval, images.length]);

  if (images.length === 0) return null;

  // Funzione per ottenere le immagini da mostrare per desktop (2 colonne)
  const getDesktopImages = (startIndex: number) => {
    const firstImage = images[startIndex % images.length];
    const secondImage = images[(startIndex + 1) % images.length];
    return [firstImage, secondImage];
  };

  return (
    <div className="mb-8">
      <h3 className="font-heading text-2xl font-semibold text-primary-red mb-6 text-center">
        {title}
      </h3>
      
      {/* Desktop Layout - 2 colonne */}
      <div className="hidden md:block">
        <div className="grid grid-cols-2 gap-6 max-w-4xl mx-auto">
          {getDesktopImages(currentIndex).map((imageSrc, index) => (
            <div key={`${currentIndex}-${index}`} className="relative aspect-[3/4] rounded-lg overflow-hidden shadow-lg group">
              <Image
                src={imageSrc}
                alt={`Piatto speciale ${(currentIndex + index) % images.length + 1}`}
                fill
                className="object-cover transition-all duration-700 group-hover:scale-105"
                sizes="(max-width: 1200px) 50vw, 25vw"
                priority={index === 0}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Mobile Layout - 1 colonna */}
      <div className="md:hidden">
        <div className="max-w-sm mx-auto">
          <div className="relative aspect-[3/4] rounded-lg overflow-hidden shadow-lg group">
            <Image
              src={images[currentIndex]}
              alt={`Piatto speciale ${currentIndex + 1}`}
              fill
              className="object-cover transition-all duration-700 group-hover:scale-105"
              sizes="100vw"
              priority
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
          </div>
        </div>
      </div>

      {/* Indicatori per il carousel */}
      <div className="flex justify-center mt-6 space-x-2">
        {images.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? 'bg-primary-red scale-125'
                : 'bg-gray-300 hover:bg-gray-400'
            }`}
            aria-label={`Vai al piatto ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
