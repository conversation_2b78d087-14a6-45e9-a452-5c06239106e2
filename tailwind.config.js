/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'primary-red': '#b22013',
        'primary-yellow': '#F7C629',
        'supporting-dark-red': '#B1220E',
        'off-white': '#F5F5F5',
        'dark-gray': '#222222',
      },
      fontFamily: {
        'heading': ['var(--font-oswald)', 'Oswald', 'sans-serif'],
        'sans': ['var(--font-source-sans)', 'Source Sans Pro', 'sans-serif'],
        'league-spartan': ['var(--font-league-spartan)', 'League Spartan', 'sans-serif'],
      },

    },
  },
  plugins: [],
}
